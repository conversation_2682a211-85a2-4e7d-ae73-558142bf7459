import { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
    Clock,
    Calendar as CalendarIcon,
    BookOpen,
    ChevronDown,
    ChevronUp,
    ExternalLink,
    BarChart,
    Edit3,
    Trash2,
    Bell,
    BellOff,
    Flag,
    Plus
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogDescription,
    DialogFooter
} from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { UpcomingTest, TestCategory } from "@/types/mockTest";
import {
    upcomingTestStorage,
    categoryStorage,
    dDayStorage,
    DDayExam,
    dDayIntegration
} from "@/utils/mockTestLocalStorage";
import { useSupabaseAuth } from "@/contexts/SupabaseAuthContext";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Switch } from "@/components/ui/switch";
import { toast } from "@/components/ui/use-toast";

interface TimeLeft {
    days: number;
    hours: number;
    minutes: number;
    seconds: number;
    totalSeconds: number;
}

export function UpcomingExamsWidget() {
    const { user } = useSupabaseAuth();
    const [exams, setExams] = useState<UpcomingTest[]>([]);
    const [categories, setCategories] = useState<TestCategory[]>([]);
    const [dDayExams, setDDayExams] = useState<Record<string, DDayExam>>({});
    const [timeLeft, setTimeLeft] = useState<Record<string, TimeLeft>>({});
    const [isExpanded, setIsExpanded] = useState(false);
    const [selectedExam, setSelectedExam] = useState<UpcomingTest | null>(null);
    const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
    const [isAddTestOpen, setIsAddTestOpen] = useState(false);
    const intervalRef = useRef<NodeJS.Timeout | null>(null);

    // Comprehensive add test form state
    const [testFormData, setTestFormData] = useState({
        name: "",
        date: new Date(),
        time: "09:00",
        categoryId: "",
        description: "",
        syllabus: [] as string[],
        testPaperUrl: "",
        isNotificationEnabled: true,
    });
    const [syllabusInput, setSyllabusInput] = useState("");

    // Load exams on component mount
    useEffect(() => {
        if (user?.id) {
            loadExams();
            loadCategories();
            loadDDayData();
        }
    }, [user?.id]);

    // Load exams from storage
    const loadExams = () => {
        if (!user?.id) return;

        // Get upcoming exams for the next 30 days
        const upcomingExams = upcomingTestStorage.getUpcoming(user.id, 30);
        setExams(upcomingExams);
    };

    // Load categories from storage
    const loadCategories = () => {
        if (!user?.id) return;
        const cats = categoryStorage.getAll(user.id);
        setCategories(cats);
    };

    // Load D-Day data from storage
    const loadDDayData = () => {
        if (!user?.id) return;
        const dDayData = dDayStorage.getAll(user.id);
        const dDayMap: Record<string, DDayExam> = {};
        dDayData.forEach(exam => {
            dDayMap[exam.id] = exam;
        });
        setDDayExams(dDayMap);
    };

    // Calculate time left for all exams
    const calculateTimeLeft = () => {
        const now = new Date();
        const updatedTimeLeft: Record<string, TimeLeft> = {};

        exams.forEach(exam => {
            const examDate = new Date(exam.date);
            // If time is available, use it; otherwise default to 9:00 AM
            if (exam.time) {
                const [hours, minutes] = exam.time.split(':').map(Number);
                examDate.setHours(hours, minutes, 0, 0);
            } else {
                examDate.setHours(9, 0, 0, 0);
            }

            const diff = examDate.getTime() - now.getTime();

            if (diff > 0) {
                const days = Math.floor(diff / (1000 * 60 * 60 * 24));
                const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((diff % (1000 * 60)) / 1000);
                const totalSeconds = diff / 1000;

                updatedTimeLeft[exam.id] = { days, hours, minutes, seconds, totalSeconds };
            }
        });

        setTimeLeft(updatedTimeLeft);
    };

    // Update countdown every second
    useEffect(() => {
        calculateTimeLeft();
        intervalRef.current = setInterval(calculateTimeLeft, 1000);

        return () => {
            if (intervalRef.current) {
                clearInterval(intervalRef.current);
            }
        };
    }, [exams]);

    // Check if an exam is urgent (within 7 days)
    const isUrgent = (examId: string): boolean => {
        return timeLeft[examId]?.days < 7;
    };

    // Get preparation progress for an exam
    const getPreparationProgress = (exam: UpcomingTest): number => {
        if (!exam.syllabus) return 0;

        // If using unified syllabus structure
        if (exam.syllabus.overallProgress !== undefined) {
            return exam.syllabus.overallProgress;
        }

        // If using legacy syllabus structure
        if (exam.syllabus_legacy && exam.syllabus_legacy.length > 0) {
            // This is a simplified calculation - in a real app, you'd track completion status
            return 0; // Default to 0 for legacy structure
        }

        return 0;
    };

    // Open exam detail modal
    const openExamDetail = (exam: UpcomingTest) => {
        setSelectedExam(exam);
        setIsDetailModalOpen(true);
    };

    // Format study materials for display
    const formatStudyMaterials = (materials?: string[]): string[] => {
        if (!materials || materials.length === 0) {
            return ["No study materials added"];
        }
        return materials;
    };

    // Get days left text with appropriate styling
    const getDaysLeftText = (days: number) => {
        if (days === 0) return "Today!";
        if (days === 1) return "Tomorrow!";
        return `${days} days left`;
    };

    // Get category information
    const getCategoryInfo = (categoryId?: string) => {
        if (!categoryId) return null;
        return categories.find(cat => cat.id === categoryId);
    };

    // Get priority colors from D-Day integration
    const getPriorityColors = (testId: string) => {
        const dDayExam = dDayExams[testId];
        if (!dDayExam) return null;

        switch (dDayExam.priority) {
            case 'high':
                return {
                    bg: 'bg-red-50 dark:bg-red-900/10',
                    border: 'border-red-200 dark:border-red-800',
                    text: 'text-red-700 dark:text-red-400',
                    indicator: 'bg-red-500'
                };
            case 'medium':
                return {
                    bg: 'bg-orange-50 dark:bg-orange-900/10',
                    border: 'border-orange-200 dark:border-orange-800',
                    text: 'text-orange-700 dark:text-orange-400',
                    indicator: 'bg-orange-500'
                };
            case 'low':
                return {
                    bg: 'bg-green-50 dark:bg-green-900/10',
                    border: 'border-green-200 dark:border-green-800',
                    text: 'text-green-700 dark:text-green-400',
                    indicator: 'bg-green-500'
                };
            default:
                return null;
        }
    };

    // Get urgency badge text
    const getUrgencyBadge = (daysLeft: number) => {
        if (daysLeft <= 1) return { text: "URGENT", variant: "destructive" as const };
        if (daysLeft <= 3) return { text: "SOON", variant: "secondary" as const };
        if (daysLeft <= 7) return { text: "THIS WEEK", variant: "outline" as const };
        return { text: "UPCOMING", variant: "outline" as const };
    };

    // Toggle notification for exam
    const toggleNotification = (examId: string, currentState: boolean) => {
        if (!user?.id) return;

        try {
            upcomingTestStorage.update(examId, {
                isNotificationEnabled: !currentState
            });
            loadExams(); // Reload to reflect changes
        } catch (error) {
            console.error('Error toggling notification:', error);
        }
    };

    // Delete exam
    const deleteExam = (examId: string) => {
        if (!user?.id) return;

        try {
            upcomingTestStorage.delete(user.id, examId);
            // Also remove from D-Day if exists
            const dDayExam = dDayExams[examId];
            if (dDayExam) {
                dDayStorage.delete(user.id, dDayExam.id);
            }
            loadExams();
            loadDDayData();
        } catch (error) {
            console.error('Error deleting exam:', error);
        }
    };

    // Edit exam (placeholder - would open edit modal)
    const editExam = (exam: UpcomingTest) => {
        // This would typically open an edit modal
        // For now, we'll just open the detail modal
        openExamDetail(exam);
    };

    // Reset test form
    const resetTestForm = () => {
        setTestFormData({
            name: "",
            date: new Date(),
            time: "09:00",
            categoryId: "",
            description: "",
            syllabus: [],
            testPaperUrl: "",
            isNotificationEnabled: true,
        });
        setSyllabusInput("");
    };

    // Handle adding syllabus topic
    const addSyllabusTopic = () => {
        if (syllabusInput.trim() && !testFormData.syllabus.includes(syllabusInput.trim())) {
            setTestFormData(prev => ({
                ...prev,
                syllabus: [...prev.syllabus, syllabusInput.trim()]
            }));
            setSyllabusInput("");
        }
    };

    // Handle removing syllabus topic
    const removeSyllabusTopic = (index: number) => {
        setTestFormData(prev => ({
            ...prev,
            syllabus: prev.syllabus.filter((_, i) => i !== index)
        }));
    };

    // Handle test creation
    const handleCreateTest = async () => {
        if (!user?.id || !testFormData.name.trim()) {
            toast({
                title: "Error",
                description: "Please fill in the test name",
                variant: "destructive",
            });
            return;
        }

        try {
            const newTest = upcomingTestStorage.create({
                name: testFormData.name,
                date: format(testFormData.date, "yyyy-MM-dd"),
                time: testFormData.time,
                categoryId: testFormData.categoryId || undefined,
                description: testFormData.description || undefined,
                syllabus: {
                    topics: testFormData.syllabus,
                    overallProgress: 0
                },
                testPaperUrl: testFormData.testPaperUrl || undefined,
                isNotificationEnabled: testFormData.isNotificationEnabled,
                userId: user.id,
            });

            // Sync with D-Day if test is created
            if (newTest) {
                try {
                    dDayIntegration.syncWithDDay(newTest);
                } catch (dDayError) {
                    console.warn("Failed to sync with D-Day:", dDayError);
                }
            }

            loadExams();
            loadDDayData();
            resetTestForm();
            setIsAddTestOpen(false);

            toast({
                title: "Test added!",
                description: `"${testFormData.name}" has been added to your upcoming tests`,
                duration: 3000,
            });
        } catch (error) {
            console.error('Error creating test:', error);
            toast({
                title: "Error",
                description: "Failed to create test",
                variant: "destructive",
                duration: 3000,
            });
        }
    };

    // Get color based on days left
    const getUrgencyColor = (days: number) => {
        if (days <= 1) return "text-red-600 dark:text-red-400";
        if (days <= 3) return "text-orange-600 dark:text-orange-400";
        if (days <= 7) return "text-yellow-600 dark:text-yellow-400";
        return "text-green-600 dark:text-green-400";
    };

    // Get progress color based on percentage
    const getProgressColor = (progress: number) => {
        if (progress < 30) return "bg-red-600";
        if (progress < 70) return "bg-yellow-600";
        return "bg-green-600";
    };

    // If no exams are available
    if (exams.length === 0) {
        return (
            <Card className="border-l-4 border-l-rose-500 h-full">
                <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-lg font-semibold">
                        <Calendar className="h-5 w-5 text-rose-500" />
                        Upcoming Exams
                    </CardTitle>
                </CardHeader>
                <CardContent className="flex flex-col items-center justify-center py-6">
                    <motion.div
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        className="p-4 rounded-full bg-rose-50 dark:bg-rose-900/20 mb-4"
                    >
                        <BookOpen className="h-8 w-8 text-rose-400" />
                    </motion.div>
                    <h3 className="text-lg font-medium text-gray-700 dark:text-gray-300 mb-1">
                        No upcoming exams
                    </h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400 text-center">
                        Add exams in the Mock Tests section to see them here
                    </p>
                </CardContent>
            </Card>
        );
    }

    return (
        <Card className="border-l-4 border-l-rose-500 h-full">
            <CardHeader className="pb-2">
                <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center gap-2 text-lg font-semibold">
                        <Calendar className="h-5 w-5 text-rose-500" />
                        Upcoming Exams
                    </div>
                    <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 hover:bg-rose-100 dark:hover:bg-rose-900/20"
                        onClick={() => setIsAddTestOpen(true)}
                    >
                        <Plus className="h-4 w-4 text-rose-600" />
                    </Button>
                </CardTitle>
            </CardHeader>

            <CardContent className="space-y-4">
                {/* Main exams list */}
                <div className="space-y-3">
                    {exams.slice(0, isExpanded ? undefined : 3).map((exam) => {
                        const daysLeft = timeLeft[exam.id]?.days;
                        const isExamUrgent = isUrgent(exam.id);
                        const preparationProgress = getPreparationProgress(exam);
                        const categoryInfo = getCategoryInfo(exam.categoryId);
                        const priorityColors = getPriorityColors(exam.id);
                        const urgencyBadge = daysLeft !== undefined ? getUrgencyBadge(daysLeft) : null;

                        return (
                            <motion.div
                                key={exam.id}
                                initial={{ opacity: 0, y: 10 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.3 }}
                            >
                                <Card
                                    className={cn(
                                        "overflow-hidden hover:shadow-lg transition-all duration-300 cursor-pointer relative group",
                                        "hover:scale-[1.02] hover:-translate-y-1",
                                        priorityColors ? priorityColors.bg : "bg-white dark:bg-gray-800",
                                        priorityColors ? priorityColors.border : "border-gray-200 dark:border-gray-700",
                                        isExamUrgent && !priorityColors ? "bg-rose-50/50 dark:bg-rose-900/10 border-rose-200 dark:border-rose-800" : ""
                                    )}
                                    onClick={() => openExamDetail(exam)}
                                >
                                    {/* Priority Indicator */}
                                    {priorityColors && (
                                        <div className={cn(
                                            "absolute top-0 left-0 w-1 h-full",
                                            priorityColors.indicator
                                        )} />
                                    )}
                                    <CardContent className="p-3 sm:p-4">
                                        {/* Header with title and badges */}
                                        <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-2 mb-3">
                                            <div className="flex-1 space-y-2">
                                                <div className="flex flex-col sm:flex-row sm:items-center gap-2">
                                                    <h4 className="font-medium text-sm sm:text-base line-clamp-1">
                                                        {exam.name}
                                                    </h4>
                                                    <div className="flex items-center gap-1 flex-wrap">
                                                        {urgencyBadge && (
                                                            <Badge variant={urgencyBadge.variant} className="text-xs">
                                                                {urgencyBadge.text}
                                                            </Badge>
                                                        )}
                                                        {categoryInfo && (
                                                            <Badge
                                                                variant="outline"
                                                                className="text-xs"
                                                                style={{
                                                                    borderColor: categoryInfo.color,
                                                                    color: categoryInfo.color
                                                                }}
                                                            >
                                                                {categoryInfo.name}
                                                            </Badge>
                                                        )}
                                                    </div>
                                                </div>

                                                {/* Description */}
                                                {exam.description && (
                                                    <p className="text-xs text-muted-foreground line-clamp-1">
                                                        {exam.description}
                                                    </p>
                                                )}

                                                {/* Date and Time */}
                                                <div className="flex items-center gap-3 text-xs text-muted-foreground">
                                                    <span className="flex items-center gap-1">
                                                        <Calendar className="h-3 w-3" />
                                                        {format(new Date(exam.date), 'MMM d, yyyy')}
                                                    </span>

                                                    {exam.time && (
                                                        <span className="flex items-center gap-1">
                                                            <Clock className="h-3 w-3" />
                                                            {exam.time}
                                                        </span>
                                                    )}
                                                </div>
                                            </div>

                                            {timeLeft[exam.id] && (
                                                <div className={cn(
                                                    "text-right sm:text-left sm:ml-auto",
                                                    getUrgencyColor(timeLeft[exam.id].days)
                                                )}>
                                                    <div className="text-sm sm:text-base font-bold">
                                                        {getDaysLeftText(timeLeft[exam.id].days)}
                                                    </div>
                                                    <div className="text-xs sm:text-sm">
                                                        {timeLeft[exam.id].hours}h {timeLeft[exam.id].minutes}m
                                                    </div>
                                                </div>
                                            )}
                                        </div>

                                        {/* Syllabus Topics */}
                                        {exam.syllabus?.topics && exam.syllabus.topics.length > 0 && (
                                            <div className="mt-2">
                                                <div className="flex items-center gap-1 text-xs text-muted-foreground mb-1">
                                                    <BookOpen className="h-3 w-3" />
                                                    <span>Syllabus ({exam.syllabus.topics.length} topics)</span>
                                                </div>
                                                <div className="flex flex-wrap gap-1">
                                                    {exam.syllabus.topics.slice(0, 3).map((topic, index) => (
                                                        <Badge key={index} variant="secondary" className="text-xs">
                                                            {topic}
                                                        </Badge>
                                                    ))}
                                                    {exam.syllabus.topics.length > 3 && (
                                                        <Badge variant="secondary" className="text-xs">
                                                            +{exam.syllabus.topics.length - 3} more
                                                        </Badge>
                                                    )}
                                                </div>
                                            </div>
                                        )}

                                        {/* Preparation progress */}
                                        <div className="mt-2 space-y-1">
                                            <div className="flex items-center justify-between text-xs">
                                                <span className="text-muted-foreground">Preparation</span>
                                                <span className="font-medium">{preparationProgress}%</span>
                                            </div>
                                            <Progress
                                                value={preparationProgress}
                                                className={cn("h-1.5", getProgressColor(preparationProgress))}
                                            />
                                        </div>

                                        {/* Action buttons */}
                                        <div className="mt-3 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                                            <div className="flex items-center gap-1 order-2 sm:order-1">
                                                {exam.testPaperUrl && (
                                                    <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        className="h-7 px-2 text-xs hover:bg-blue-50 dark:hover:bg-blue-900/20"
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            window.open(exam.testPaperUrl, '_blank');
                                                        }}
                                                    >
                                                        <ExternalLink className="h-3 w-3 mr-1" />
                                                        <span className="hidden sm:inline">Paper</span>
                                                    </Button>
                                                )}
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    className={cn(
                                                        "h-7 px-2 text-xs",
                                                        exam.isNotificationEnabled
                                                            ? "hover:bg-green-50 dark:hover:bg-green-900/20 text-green-600"
                                                            : "hover:bg-gray-50 dark:hover:bg-gray-800 text-gray-500"
                                                    )}
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        toggleNotification(exam.id, exam.isNotificationEnabled);
                                                    }}
                                                >
                                                    {exam.isNotificationEnabled ? (
                                                        <Bell className="h-3 w-3" />
                                                    ) : (
                                                        <BellOff className="h-3 w-3" />
                                                    )}
                                                </Button>
                                            </div>

                                            <div className="flex items-center gap-1 order-1 sm:order-2">
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    className="h-7 px-2 text-xs hover:bg-blue-50 dark:hover:bg-blue-900/20"
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        editExam(exam);
                                                    }}
                                                >
                                                    <Edit3 className="h-3 w-3" />
                                                    <span className="hidden sm:inline ml-1">Edit</span>
                                                </Button>
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    className="h-7 px-2 text-xs text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        if (confirm('Are you sure you want to delete this exam?')) {
                                                            deleteExam(exam.id);
                                                        }
                                                    }}
                                                >
                                                    <Trash2 className="h-3 w-3" />
                                                    <span className="hidden sm:inline ml-1">Delete</span>
                                                </Button>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            </motion.div>
                        );
                    })}
                </div>

                {/* Show more/less button */}
                {exams.length > 3 && (
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setIsExpanded(!isExpanded)}
                        className="w-full text-xs"
                    >
                        {isExpanded ? (
                            <>
                                <ChevronUp className="h-3 w-3 mr-1" />
                                Show Less
                            </>
                        ) : (
                            <>
                                <ChevronDown className="h-3 w-3 mr-1" />
                                Show More ({exams.length - 3} more)
                            </>
                        )}
                    </Button>
                )}
            </CardContent>

            {/* Exam detail modal */}
            <Dialog open={isDetailModalOpen} onOpenChange={setIsDetailModalOpen}>
                <DialogContent className="max-w-md sm:max-w-lg max-h-[90vh] overflow-y-auto">
                    {selectedExam && (
                        <>
                            <DialogHeader>
                                <DialogTitle className="flex items-center gap-2">
                                    <BookOpen className="h-5 w-5 text-rose-500" />
                                    {selectedExam.name}
                                    {getCategoryInfo(selectedExam.categoryId) && (
                                        <Badge
                                            variant="outline"
                                            className="text-xs"
                                            style={{
                                                borderColor: getCategoryInfo(selectedExam.categoryId)?.color,
                                                color: getCategoryInfo(selectedExam.categoryId)?.color
                                            }}
                                        >
                                            {getCategoryInfo(selectedExam.categoryId)?.name}
                                        </Badge>
                                    )}
                                </DialogTitle>
                                <DialogDescription>
                                    {selectedExam.description || "Exam details and preparation materials"}
                                </DialogDescription>
                            </DialogHeader>

                            <div className="space-y-4">
                                {/* Priority and Status */}
                                {dDayExams[selectedExam.id] && (
                                    <div className="flex items-center gap-2">
                                        <Flag className="h-4 w-4 text-muted-foreground" />
                                        <span className="text-sm text-muted-foreground">Priority:</span>
                                        <Badge
                                            variant="outline"
                                            className={cn(
                                                "text-xs",
                                                getPriorityColors(selectedExam.id)?.text
                                            )}
                                        >
                                            {dDayExams[selectedExam.id].priority.toUpperCase()}
                                        </Badge>
                                    </div>
                                )}

                                {/* Countdown and date */}
                                <div className="bg-rose-50 dark:bg-rose-900/10 rounded-lg p-4">
                                    <div className="grid grid-cols-2 gap-4">
                                        <div>
                                            <p className="text-sm text-muted-foreground">Date & Time</p>
                                            <p className="font-medium">
                                                {format(new Date(selectedExam.date), 'MMMM d, yyyy')}
                                                {selectedExam.time && ` at ${selectedExam.time}`}
                                            </p>
                                        </div>

                                        {timeLeft[selectedExam.id] && (
                                            <div className="text-right">
                                                <p className="text-sm text-muted-foreground">Countdown</p>
                                                <p className={cn(
                                                    "font-bold",
                                                    getUrgencyColor(timeLeft[selectedExam.id].days)
                                                )}>
                                                    {timeLeft[selectedExam.id].days}d {timeLeft[selectedExam.id].hours}h {timeLeft[selectedExam.id].minutes}m
                                                </p>
                                            </div>
                                        )}
                                    </div>
                                </div>

                                {/* Syllabus Topics */}
                                {selectedExam.syllabus?.topics && selectedExam.syllabus.topics.length > 0 && (
                                    <div>
                                        <h4 className="font-medium text-sm flex items-center gap-1 mb-2">
                                            <BookOpen className="h-4 w-4 text-rose-500" />
                                            Syllabus Topics ({selectedExam.syllabus.topics.length})
                                        </h4>
                                        <div className="flex flex-wrap gap-1">
                                            {selectedExam.syllabus.topics.map((topic, index) => (
                                                <Badge key={index} variant="secondary" className="text-xs">
                                                    {topic}
                                                </Badge>
                                            ))}
                                        </div>
                                    </div>
                                )}

                                {/* Notification and Test Paper */}
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-2">
                                        <span className="text-sm text-muted-foreground">Notifications:</span>
                                        <Badge variant={selectedExam.isNotificationEnabled ? "default" : "secondary"}>
                                            {selectedExam.isNotificationEnabled ? "Enabled" : "Disabled"}
                                        </Badge>
                                    </div>

                                    {selectedExam.testPaperUrl && (
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => window.open(selectedExam.testPaperUrl, '_blank')}
                                        >
                                            <ExternalLink className="h-4 w-4 mr-1" />
                                            Test Paper
                                        </Button>
                                    )}
                                </div>

                                {/* Preparation progress */}
                                <div>
                                    <div className="flex items-center justify-between mb-2">
                                        <h4 className="font-medium text-sm flex items-center gap-1">
                                            <BarChart className="h-4 w-4 text-rose-500" />
                                            Preparation Progress
                                        </h4>
                                        <span className="text-sm font-medium">
                                            {getPreparationProgress(selectedExam)}%
                                        </span>
                                    </div>
                                    <Progress
                                        value={getPreparationProgress(selectedExam)}
                                        className={cn("h-2", getProgressColor(getPreparationProgress(selectedExam)))}
                                    />
                                </div>

                                <Separator />

                                {/* Study materials */}
                                <div>
                                    <h4 className="font-medium text-sm mb-2">Study Materials</h4>
                                    <div className="space-y-2">
                                        {formatStudyMaterials(selectedExam.studyMaterials).map((material, index) => (
                                            <div
                                                key={index}
                                                className="text-sm p-2 rounded-md bg-slate-50 dark:bg-slate-800 flex items-center justify-between"
                                            >
                                                <span className="truncate flex-1">{material}</span>
                                                {material.startsWith('http') && (
                                                    <TooltipProvider>
                                                        <Tooltip>
                                                            <TooltipTrigger asChild>
                                                                <Button
                                                                    size="icon"
                                                                    variant="ghost"
                                                                    className="h-6 w-6"
                                                                    onClick={(e) => {
                                                                        e.stopPropagation();
                                                                        window.open(material, '_blank');
                                                                    }}
                                                                >
                                                                    <ExternalLink className="h-3 w-3" />
                                                                </Button>
                                                            </TooltipTrigger>
                                                            <TooltipContent>
                                                                <p>Open link</p>
                                                            </TooltipContent>
                                                        </Tooltip>
                                                    </TooltipProvider>
                                                )}
                                            </div>
                                        ))}
                                    </div>
                                </div>

                                {/* Notes */}
                                {selectedExam.notes && (
                                    <div>
                                        <h4 className="font-medium text-sm mb-2">Notes</h4>
                                        <div className="text-sm p-3 rounded-md bg-slate-50 dark:bg-slate-800">
                                            {selectedExam.notes}
                                        </div>
                                    </div>
                                )}
                            </div>

                            <DialogFooter>
                                <Button
                                    variant="outline"
                                    onClick={() => setIsDetailModalOpen(false)}
                                >
                                    Close
                                </Button>
                            </DialogFooter>
                        </>
                    )}
                </DialogContent>
            </Dialog>

            {/* Comprehensive Add Test Dialog */}
            <Dialog open={isAddTestOpen} onOpenChange={setIsAddTestOpen}>
                <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
                    <DialogHeader>
                        <DialogTitle className="flex items-center gap-2">
                            <Plus className="h-5 w-5 text-rose-500" />
                            Add Upcoming Test
                        </DialogTitle>
                        <DialogDescription>
                            Create a new upcoming test with comprehensive details
                        </DialogDescription>
                    </DialogHeader>

                    <div className="grid gap-4 py-4">
                        {/* Test Name */}
                        <div className="grid gap-2">
                            <label htmlFor="testName" className="text-sm font-medium">
                                Test Name *
                            </label>
                            <Input
                                id="testName"
                                value={testFormData.name}
                                onChange={(e) => setTestFormData(prev => ({ ...prev, name: e.target.value }))}
                                placeholder="Enter test name"
                                className="bg-muted border-border"
                            />
                        </div>

                        {/* Date and Time */}
                        <div className="grid grid-cols-2 gap-4">
                            <div className="grid gap-2">
                                <label htmlFor="testDate" className="text-sm font-medium">
                                    Date *
                                </label>
                                <Popover>
                                    <PopoverTrigger asChild>
                                        <Button
                                            variant="outline"
                                            className="justify-start text-left font-normal bg-muted border-border"
                                        >
                                            <CalendarIcon className="mr-2 h-4 w-4" />
                                            {format(testFormData.date, 'PPP')}
                                        </Button>
                                    </PopoverTrigger>
                                    <PopoverContent className="w-auto p-0">
                                        <Calendar
                                            mode="single"
                                            selected={testFormData.date}
                                            onSelect={(date) => date && setTestFormData(prev => ({ ...prev, date }))}
                                            initialFocus
                                        />
                                    </PopoverContent>
                                </Popover>
                            </div>

                            <div className="grid gap-2">
                                <label htmlFor="testTime" className="text-sm font-medium">
                                    Time
                                </label>
                                <Input
                                    id="testTime"
                                    type="time"
                                    value={testFormData.time}
                                    onChange={(e) => setTestFormData(prev => ({ ...prev, time: e.target.value }))}
                                    className="bg-muted border-border"
                                />
                            </div>
                        </div>

                        {/* Category */}
                        <div className="grid gap-2">
                            <label htmlFor="category" className="text-sm font-medium">
                                Category
                            </label>
                            <Select
                                value={testFormData.categoryId}
                                onValueChange={(value) => setTestFormData(prev => ({ ...prev, categoryId: value }))}
                            >
                                <SelectTrigger className="bg-muted border-border">
                                    <SelectValue placeholder="Select category" />
                                </SelectTrigger>
                                <SelectContent>
                                    {categories.map((category) => (
                                        <SelectItem key={category.id} value={category.id}>
                                            <div className="flex items-center gap-2">
                                                <div
                                                    className="w-3 h-3 rounded-full"
                                                    style={{ backgroundColor: category.color }}
                                                />
                                                {category.name}
                                            </div>
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>

                        {/* Description */}
                        <div className="grid gap-2">
                            <label htmlFor="description" className="text-sm font-medium">
                                Description
                            </label>
                            <Textarea
                                id="description"
                                value={testFormData.description}
                                onChange={(e) => setTestFormData(prev => ({ ...prev, description: e.target.value }))}
                                placeholder="Describe the test, topics covered, or any special instructions"
                                rows={3}
                                className="bg-muted border-border"
                            />
                        </div>

                        {/* Syllabus Topics */}
                        <div className="grid gap-2">
                            <label className="text-sm font-medium">
                                Syllabus Topics
                            </label>
                            <div className="flex gap-2">
                                <Input
                                    value={syllabusInput}
                                    onChange={(e) => setSyllabusInput(e.target.value)}
                                    placeholder="Add a topic"
                                    className="bg-muted border-border"
                                    onKeyDown={(e) => {
                                        if (e.key === 'Enter') {
                                            e.preventDefault();
                                            addSyllabusTopic();
                                        }
                                    }}
                                />
                                <Button
                                    type="button"
                                    variant="outline"
                                    size="sm"
                                    onClick={addSyllabusTopic}
                                    className="px-3"
                                >
                                    <Plus className="h-4 w-4" />
                                </Button>
                            </div>
                            {testFormData.syllabus.length > 0 && (
                                <div className="flex flex-wrap gap-1 mt-2">
                                    {testFormData.syllabus.map((topic, index) => (
                                        <Badge
                                            key={index}
                                            variant="secondary"
                                            className="text-xs cursor-pointer hover:bg-destructive hover:text-destructive-foreground"
                                            onClick={() => removeSyllabusTopic(index)}
                                        >
                                            {topic} ×
                                        </Badge>
                                    ))}
                                </div>
                            )}
                        </div>

                        {/* Test Paper URL */}
                        <div className="grid gap-2">
                            <label htmlFor="testPaperUrl" className="text-sm font-medium">
                                Test Paper URL
                            </label>
                            <Input
                                id="testPaperUrl"
                                value={testFormData.testPaperUrl}
                                onChange={(e) => setTestFormData(prev => ({ ...prev, testPaperUrl: e.target.value }))}
                                placeholder="https://example.com/test-paper.pdf"
                                className="bg-muted border-border"
                            />
                        </div>

                        {/* Notification Settings */}
                        <div className="flex items-center justify-between">
                            <div className="space-y-0.5">
                                <label className="text-sm font-medium">
                                    Enable Notifications
                                </label>
                                <p className="text-xs text-muted-foreground">
                                    Get reminders before the test
                                </p>
                            </div>
                            <Switch
                                checked={testFormData.isNotificationEnabled}
                                onCheckedChange={(checked) => setTestFormData(prev => ({ ...prev, isNotificationEnabled: checked }))}
                            />
                        </div>
                    </div>

                    <DialogFooter>
                        <Button variant="outline" onClick={() => setIsAddTestOpen(false)}>
                            Cancel
                        </Button>
                        <Button onClick={handleCreateTest} className="bg-rose-600 hover:bg-rose-700">
                            Add Test
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </Card>
    );
}