import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '../ui/card';
import { TodaysTasksWidget } from './TodaysTasksWidget';
import { UpcomingExamsWidget } from './UpcomingExamsWidget';
import { 
  CheckSquare, 
  Calendar, 
  BarChart3, 
  Brain, 
  Trophy, 
  Flame, 
  Timer, 
  <PERSON><PERSON><PERSON> 
} from 'lucide-react';

interface DashboardGridProps {
  userId: string;
}

export const DashboardGrid: React.FC<DashboardGridProps> = ({ userId }) => {
  // Placeholder widgets - these will be replaced with actual widget components in later tasks
  const widgets = [
    {
      id: 'tasks',
      title: "Today's Tasks",
      icon: CheckSquare,
      color: 'text-orange-500',
      bgColor: 'bg-orange-50 dark:bg-orange-900/10',
      borderColor: 'border-l-orange-500',
      content: 'Task management widget will be implemented in task 4'
    },
    {
      id: 'exams',
      title: 'Upcoming Exams',
      icon: Calendar,
      color: 'text-rose-500',
      bgColor: 'bg-rose-50 dark:bg-rose-900/10',
      borderColor: 'border-l-rose-500',
      content: 'Exam countdown widget will be implemented in task 5'
    },
    {
      id: 'progress',
      title: 'Chapter Progress',
      icon: BarChart3,
      color: 'text-emerald-500',
      bgColor: 'bg-emerald-50 dark:bg-emerald-900/10',
      borderColor: 'border-l-emerald-500',
      content: 'Progress tracking widget will be implemented in task 7'
    },
    {
      id: 'swot',
      title: 'SWOT Analysis',
      icon: Brain,
      color: 'text-violet-500',
      bgColor: 'bg-violet-50 dark:bg-violet-900/10',
      borderColor: 'border-l-violet-500',
      content: 'AI-powered SWOT widget will be implemented in task 8'
    },
    {
      id: 'leaderboard',
      title: 'Productivity Leaderboard',
      icon: Trophy,
      color: 'text-indigo-500',
      bgColor: 'bg-indigo-50 dark:bg-indigo-900/10',
      borderColor: 'border-l-indigo-500',
      content: 'Leaderboard widget will be implemented in task 9'
    },
    {
      id: 'streak',
      title: 'Study Streak',
      icon: Flame,
      color: 'text-teal-500',
      bgColor: 'bg-teal-50 dark:bg-teal-900/10',
      borderColor: 'border-l-teal-500',
      content: 'Streak tracking widget will be implemented in task 10'
    },
    {
      id: 'countdown',
      title: 'D-Day Countdown',
      icon: Timer,
      color: 'text-pink-500',
      bgColor: 'bg-pink-50 dark:bg-pink-900/10',
      borderColor: 'border-l-pink-500',
      content: 'Countdown widget will be implemented in task 6'
    },
    {
      id: 'analytics',
      title: 'Analytics Overview',
      icon: PieChart,
      color: 'text-slate-500',
      bgColor: 'bg-slate-50 dark:bg-slate-900/10',
      borderColor: 'border-l-slate-500',
      content: 'Analytics widget will be implemented in task 12'
    }
  ];

  return (
    <div className="space-y-8">
      {/* Welcome Section */}
      <div className="mb-12">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-3">
          Welcome to your Dashboard
        </h1>
        <p className="text-lg text-gray-600 dark:text-gray-400">
          Here's an overview of your academic progress and activities
        </p>
      </div>

      {/* Dashboard Grid - Optimized for better spacing */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Today's Tasks Widget - Implemented */}
        <div className="w-full">
          <TodaysTasksWidget />
        </div>

        {/* Upcoming Exams Widget - Implemented */}
        <div className="w-full">
          <UpcomingExamsWidget />
        </div>
      </div>

      {/* Additional Widgets Grid - 2 columns for better spacing */}
      <div className="mt-8 grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Other widgets - placeholders for future implementation */}
        {widgets.filter(widget => widget.id !== 'tasks' && widget.id !== 'exams').map((widget) => {
          const Icon = widget.icon;
          
          return (
            <Card 
              key={widget.id}
              className={`${widget.bgColor} border-l-4 ${widget.borderColor} hover:shadow-lg transition-all duration-300 h-full`}
            >
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-3 text-xl">
                  <Icon className={`h-6 w-6 ${widget.color}`} />
                  <span className="text-gray-800 dark:text-gray-200">
                    {widget.title}
                  </span>
                </CardTitle>
              </CardHeader>
              <CardContent className="pb-6">
                <div className="text-sm text-gray-600 dark:text-gray-400 p-6 bg-white/50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700">
                  {widget.content}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Quick Stats Section */}
      <div className="mt-12 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 hover:shadow-lg transition-all duration-300">
          <CardContent className="p-8">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-600 dark:text-blue-400 mb-2">
                  Study Time Today
                </p>
                <p className="text-3xl font-bold text-blue-900 dark:text-blue-100">
                  2h 45m
                </p>
              </div>
              <div className="w-14 h-14 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center">
                <Timer className="h-7 w-7 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 hover:shadow-lg transition-all duration-300">
          <CardContent className="p-8">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-600 dark:text-green-400 mb-2">
                  Tasks Completed
                </p>
                <p className="text-3xl font-bold text-green-900 dark:text-green-100">
                  8/12
                </p>
              </div>
              <div className="w-14 h-14 bg-green-100 dark:bg-green-800 rounded-full flex items-center justify-center">
                <CheckSquare className="h-7 w-7 text-green-600 dark:text-green-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 hover:shadow-lg transition-all duration-300">
          <CardContent className="p-8">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-600 dark:text-purple-400 mb-2">
                  Current Streak
                </p>
                <p className="text-3xl font-bold text-purple-900 dark:text-purple-100">
                  7 days
                </p>
              </div>
              <div className="w-14 h-14 bg-purple-100 dark:bg-purple-800 rounded-full flex items-center justify-center">
                <Flame className="h-7 w-7 text-purple-600 dark:text-purple-400" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Work in Progress Banner */}
      <div className="mt-12 mb-8">
        <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-amber-50 via-orange-50 to-red-50 dark:from-amber-900/20 dark:via-orange-900/20 dark:to-red-900/20 border border-amber-200 dark:border-amber-800/50 shadow-lg">
          {/* Animated Background Pattern */}
          <div className="absolute inset-0 opacity-10 dark:opacity-5">
            <div className="absolute top-4 left-4 w-8 h-8 bg-amber-400 rounded-full animate-pulse"></div>
            <div className="absolute top-8 right-12 w-6 h-6 bg-orange-400 rounded-full animate-pulse delay-300"></div>
            <div className="absolute bottom-6 left-12 w-4 h-4 bg-red-400 rounded-full animate-pulse delay-700"></div>
            <div className="absolute bottom-4 right-8 w-10 h-10 bg-amber-300 rounded-full animate-pulse delay-1000"></div>
          </div>

          {/* Main Content */}
          <div className="relative p-8 text-center">
            {/* Construction Icon */}
            <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-amber-400 to-orange-500 rounded-full mb-6 shadow-lg">
              <svg className="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z" />
                <path d="M19 14L20.09 17.26L24 18L20.09 18.74L19 22L17.91 18.74L14 18L17.91 17.26L19 14Z" />
                <path d="M5 14L6.09 17.26L10 18L6.09 18.74L5 22L3.91 18.74L0 18L3.91 17.26L5 14Z" />
              </svg>
            </div>

            {/* Title */}
            <h3 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white mb-4">
              🚧 Dashboard Under Construction 🚧
            </h3>

            {/* Description */}
            <p className="text-lg text-gray-700 dark:text-gray-300 mb-6 max-w-2xl mx-auto leading-relaxed">
              We're building something amazing for you! The developer is working hard to create the most intuitive and powerful dashboard experience.
            </p>

            {/* Features Coming Soon */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
              {[
                { icon: '📊', title: 'Analytics', desc: 'Detailed insights' },
                { icon: '✅', title: 'Tasks', desc: 'Smart management' },
                { icon: '📚', title: 'Study Tools', desc: 'Enhanced learning' },
                { icon: '🎯', title: 'Goals', desc: 'Progress tracking' }
              ].map((feature, index) => (
                <div key={index} className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-xl p-4 border border-amber-200/50 dark:border-amber-700/50">
                  <div className="text-2xl mb-2">{feature.icon}</div>
                  <h4 className="font-semibold text-gray-900 dark:text-white text-sm">{feature.title}</h4>
                  <p className="text-xs text-gray-600 dark:text-gray-400">{feature.desc}</p>
                </div>
              ))}
            </div>

            {/* Progress Bar */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Development Progress</span>
                <span className="text-sm font-bold text-amber-600 dark:text-amber-400">13%</span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 overflow-hidden">
                <div className="bg-gradient-to-r from-amber-400 via-orange-500 to-red-500 h-3 rounded-full transition-all duration-1000 ease-out" style={{ width: '13%' }}></div>
              </div>
            </div>

            {/* Call to Action */}
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
              <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span>Currently in active development</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                <span>🔔</span>
                <span>You'll be notified when ready!</span>
              </div>
            </div>
          </div>

          {/* Decorative Elements */}
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-amber-200/30 to-transparent rounded-full -translate-y-16 translate-x-16"></div>
          <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-orange-200/30 to-transparent rounded-full translate-y-12 -translate-x-12"></div>
        </div>
      </div>
    </div>
  );
};

export default DashboardGrid;