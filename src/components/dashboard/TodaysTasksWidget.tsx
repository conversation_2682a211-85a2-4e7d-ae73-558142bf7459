import React, { useState, useEffect, useMemo } from 'react';
import { DragDropContext, Droppable, Draggable, DropResult } from '@hello-pangea/dnd';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Textarea } from '../ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '../ui/dialog';
import { Badge } from '../ui/badge';
import { Checkbox } from '../ui/checkbox';
import { useSupabaseTodoStore } from '@/stores/supabaseTodoStore';
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext';
import { TodoItem } from '@/types/todo';
import { 
  CheckSquare, 
  Plus, 
  Clock, 
  AlertCircle, 
  GripVertical,
  Edit3,
  Trash2
} from 'lucide-react';
import { format, isToday, startOfDay, isBefore } from 'date-fns';
import { toast } from '@/components/ui/use-toast';

interface TodaysTasksWidgetProps {
  className?: string;
}

interface TaskItemProps {
  task: TodoItem;
  index: number;
  onComplete: (taskId: string, completed: boolean) => void;
  onEdit: (task: TodoItem) => void;
  onDelete: (taskId: string) => void;
}

const TaskItem: React.FC<TaskItemProps> = ({ task, index, onComplete, onEdit, onDelete }) => {
  const [isCompleted, setIsCompleted] = useState(false);

  // Check if task is in "Done" column
  const { board } = useSupabaseTodoStore();
  const isDone = board.columns['column-3']?.taskIds.includes(task.id) || false;

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'border-l-red-400 bg-red-50 dark:bg-red-900/10';
      case 'medium':
        return 'border-l-amber-400 bg-amber-50 dark:bg-amber-900/10';
      case 'low':
        return 'border-l-green-400 bg-green-50 dark:bg-green-900/10';
      default:
        return 'border-l-blue-400 bg-blue-50 dark:bg-blue-900/10';
    }
  };

  const getPriorityBadgeColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-500 hover:bg-red-600 text-white';
      case 'medium':
        return 'bg-amber-500 hover:bg-amber-600 text-white';
      case 'low':
        return 'bg-green-500 hover:bg-green-600 text-white';
      default:
        return 'bg-blue-500 hover:bg-blue-600 text-white';
    }
  };

  const isOverdue = (): boolean => {
    if (!task.dueDate) return false;
    const today = startOfDay(new Date());
    const taskDueDate = startOfDay(new Date(task.dueDate));
    return isBefore(taskDueDate, today) && !isDone;
  };

  const handleComplete = (checked: boolean) => {
    setIsCompleted(checked);
    onComplete(task.id, checked);
  };

  return (
    <Draggable draggableId={task.id} index={index}>
      {(provided, snapshot) => (
        <div
          ref={provided.innerRef}
          {...provided.draggableProps}
          className={`mb-2 transition-all duration-200 ${
            snapshot.isDragging ? 'shadow-lg scale-[1.02] z-10 rotate-1' : ''
          }`}
          style={{
            ...provided.draggableProps.style,
          }}
        >
          <div
            className={`
              flex items-center gap-3 p-3 rounded-lg border-l-2 transition-all duration-150
              ${getPriorityColor(task.priority)}
              ${isOverdue() ? 'border-red-500 bg-red-100 dark:bg-red-900/20' : ''}
              ${isDone ? 'opacity-60' : 'hover:shadow-sm'}
              ${snapshot.isDragging ? 'bg-white dark:bg-gray-800 shadow-xl' : ''}
            `}
          >
            {/* Drag Handle */}
            <div
              {...provided.dragHandleProps}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 cursor-grab active:cursor-grabbing"
            >
              <GripVertical className="h-4 w-4" />
            </div>

            {/* Completion Checkbox */}
            <Checkbox
              checked={isDone || isCompleted}
              onCheckedChange={handleComplete}
              className="data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500"
            />

            {/* Task Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between gap-2">
                <div className="flex-1 min-w-0">
                  <h4 className={`text-sm font-medium text-gray-800 dark:text-gray-200 truncate ${
                    isDone || isCompleted ? 'line-through text-gray-500' : ''
                  }`}>
                    {task.title}
                  </h4>
                  {task.description && (
                    <p className="text-xs text-gray-600 dark:text-gray-400 mt-1 line-clamp-2">
                      {task.description}
                    </p>
                  )}
                </div>

                {/* Priority Badge */}
                <Badge className={`${getPriorityBadgeColor(task.priority)} text-xs`}>
                  {task.priority}
                </Badge>
              </div>

              {/* Due Date and Actions */}
              <div className="flex items-center justify-between mt-2">
                <div className="flex items-center gap-2">
                  {task.dueDate && (
                    <div className={`flex items-center text-xs ${
                      isOverdue() 
                        ? 'text-red-600 dark:text-red-400 font-semibold' 
                        : 'text-gray-500 dark:text-gray-400'
                    }`}>
                      <Clock className="h-3 w-3 mr-1" />
                      {format(new Date(task.dueDate), 'MMM d')}
                      {isOverdue() && <AlertCircle className="h-3 w-3 ml-1" />}
                    </div>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 hover:bg-blue-100 dark:hover:bg-blue-900/20"
                    onClick={(e) => {
                      e.stopPropagation();
                      onEdit(task);
                    }}
                  >
                    <Edit3 className="h-3 w-3 text-blue-600" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 hover:bg-red-100 dark:hover:bg-red-900/20"
                    onClick={(e) => {
                      e.stopPropagation();
                      onDelete(task.id);
                    }}
                  >
                    <Trash2 className="h-3 w-3 text-red-600" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </Draggable>
  );
};

export const TodaysTasksWidget: React.FC<TodaysTasksWidgetProps> = ({ className }) => {
  const { user } = useSupabaseAuth();
  const { board, fetchTodos, updateTask, addTask, deleteTask, moveTask } = useSupabaseTodoStore();
  
  // Quick add task form state
  const [isQuickAddOpen, setIsQuickAddOpen] = useState(false);
  const [quickTaskTitle, setQuickTaskTitle] = useState('');
  const [quickTaskPriority, setQuickTaskPriority] = useState<'low' | 'medium' | 'high'>('medium');
  const [quickTaskEstimation, setQuickTaskEstimation] = useState('');

  // Edit task dialog state
  const [editingTask, setEditingTask] = useState<TodoItem | null>(null);
  const [editTitle, setEditTitle] = useState('');
  const [editDescription, setEditDescription] = useState('');
  const [editPriority, setEditPriority] = useState<'low' | 'medium' | 'high'>('medium');

  // Get today's tasks
  const todaysTasks = useMemo(() => {
    const allTasks: TodoItem[] = [];
    
    // Get tasks from Todo and In Progress columns only (not Done)
    ['column-1', 'column-2'].forEach(columnId => {
      const column = board.columns[columnId];
      if (column) {
        column.taskIds.forEach(taskId => {
          const task = board.tasks[taskId];
          if (task) {
            // Include tasks that are due today or overdue, or have no due date
            if (!task.dueDate || isToday(new Date(task.dueDate)) || isBefore(new Date(task.dueDate), startOfDay(new Date()))) {
              allTasks.push(task);
            }
          }
        });
      }
    });

    // Sort by priority (high -> medium -> low) and then by due date
    return allTasks.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      const aPriority = priorityOrder[a.priority] || 0;
      const bPriority = priorityOrder[b.priority] || 0;
      
      if (aPriority !== bPriority) {
        return bPriority - aPriority;
      }
      
      // If same priority, sort by due date
      if (a.dueDate && b.dueDate) {
        return a.dueDate - b.dueDate;
      }
      if (a.dueDate) return -1;
      if (b.dueDate) return 1;
      return 0;
    });
  }, [board]);

  // Fetch tasks on component mount
  useEffect(() => {
    if (user) {
      fetchTodos(user.id);
    }
  }, [user, fetchTodos]);

  const handleTaskComplete = async (taskId: string, completed: boolean) => {
    try {
      // Move task to appropriate column based on completion status
      const sourceColumn = Object.keys(board.columns).find(columnId => 
        board.columns[columnId].taskIds.includes(taskId)
      );
      
      if (!sourceColumn) return;

      const sourceIndex = board.columns[sourceColumn].taskIds.indexOf(taskId);
      const targetColumn = completed ? 'column-3' : 'column-1'; // Done or Todo
      const targetIndex = board.columns[targetColumn].taskIds.length;

      await moveTask(
        { droppableId: sourceColumn, index: sourceIndex, taskId },
        { droppableId: targetColumn, index: targetIndex, taskId }
      );

      toast({
        title: completed ? "Task completed!" : "Task moved to Todo",
        description: `"${board.tasks[taskId]?.title}" has been ${completed ? 'completed' : 'moved back to Todo'}`,
        duration: 3000,
      });
    } catch (error) {
      console.error('Error updating task completion:', error);
      toast({
        title: "Error",
        description: "Failed to update task completion",
        variant: "destructive",
        duration: 3000,
      });
    }
  };

  const handleQuickAdd = async () => {
    if (!user || !quickTaskTitle.trim()) return;

    try {
      const taskData = {
        title: quickTaskTitle,
        description: `Quick task - ${quickTaskEstimation ? `Estimated time: ${quickTaskEstimation}` : 'No time estimate'}`,
        priority: quickTaskPriority,
        createdBy: user.id,
        dueDate: new Date().getTime(), // Due today
      };

      await addTask(taskData);

      // Reset form
      setQuickTaskTitle('');
      setQuickTaskPriority('medium');
      setQuickTaskEstimation('');
      setIsQuickAddOpen(false);

      toast({
        title: "Task added!",
        description: `"${quickTaskTitle}" has been added to your tasks`,
        duration: 3000,
      });
    } catch (error) {
      console.error('Error adding quick task:', error);
      toast({
        title: "Error",
        description: "Failed to add task",
        variant: "destructive",
        duration: 3000,
      });
    }
  };

  const handleEditTask = (task: TodoItem) => {
    setEditingTask(task);
    setEditTitle(task.title);
    setEditDescription(task.description);
    setEditPriority(task.priority);
  };

  const handleSaveEdit = async () => {
    if (!editingTask || !editTitle.trim()) return;

    try {
      await updateTask(editingTask.id, {
        title: editTitle,
        description: editDescription,
        priority: editPriority,
      });

      setEditingTask(null);
      toast({
        title: "Task updated!",
        description: `"${editTitle}" has been updated`,
        duration: 3000,
      });
    } catch (error) {
      console.error('Error updating task:', error);
      toast({
        title: "Error",
        description: "Failed to update task",
        variant: "destructive",
        duration: 3000,
      });
    }
  };

  const handleDeleteTask = async (taskId: string) => {
    try {
      const taskTitle = board.tasks[taskId]?.title || 'Task';
      await deleteTask(taskId);
      
      toast({
        title: "Task deleted",
        description: `"${taskTitle}" has been deleted`,
        duration: 3000,
      });
    } catch (error) {
      console.error('Error deleting task:', error);
      toast({
        title: "Error",
        description: "Failed to delete task",
        variant: "destructive",
        duration: 3000,
      });
    }
  };

  const handleDragEnd = async (result: DropResult) => {
    const { destination, source } = result;

    if (!destination || (destination.droppableId === source.droppableId && destination.index === source.index)) {
      return;
    }

    try {
      // For the widget, we only allow reordering within the same list
      // The actual column movement is handled by task completion
      if (destination.droppableId === source.droppableId) {
        // This is just reordering within today's tasks
        // We don't need to do anything special here as the visual reorder is handled by the drag system
        // The actual task order in Supabase would need additional implementation
        toast({
          title: "Task reordered",
          description: "Task order has been updated",
          duration: 2000,
        });
      }
    } catch (error) {
      console.error('Error reordering task:', error);
      toast({
        title: "Error",
        description: "Failed to reorder task",
        variant: "destructive",
        duration: 3000,
      });
    }
  };

  const completedTasksCount = todaysTasks.filter(task => 
    board.columns['column-3']?.taskIds.includes(task.id)
  ).length;

  const overdueTasksCount = todaysTasks.filter(task => {
    if (!task.dueDate) return false;
    const today = startOfDay(new Date());
    const taskDueDate = startOfDay(new Date(task.dueDate));
    const isDone = board.columns['column-3']?.taskIds.includes(task.id);
    return isBefore(taskDueDate, today) && !isDone;
  }).length;

  return (
    <Card className={`bg-orange-50 dark:bg-orange-900/10 border-l-4 border-l-orange-500 ${className}`}>
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <CheckSquare className="h-5 w-5 text-orange-500" />
            <span className="text-gray-800 dark:text-gray-200">Today's Tasks</span>
          </div>
          <div className="flex items-center gap-2">
            {overdueTasksCount > 0 && (
              <Badge variant="destructive" className="text-xs">
                {overdueTasksCount} overdue
              </Badge>
            )}
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 hover:bg-orange-100 dark:hover:bg-orange-900/20"
              onClick={() => setIsQuickAddOpen(true)}
            >
              <Plus className="h-4 w-4 text-orange-600" />
            </Button>
          </div>
        </CardTitle>
        
        {/* Progress Summary */}
        <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
          <span>{completedTasksCount} completed</span>
          <span>•</span>
          <span>{todaysTasks.length - completedTasksCount} remaining</span>
          {todaysTasks.length > 0 && (
            <>
              <span>•</span>
              <span>{Math.round((completedTasksCount / todaysTasks.length) * 100)}% done</span>
            </>
          )}
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {todaysTasks.length === 0 ? (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            <CheckSquare className="h-12 w-12 mx-auto mb-3 opacity-50" />
            <p className="text-sm">No tasks for today</p>
            <p className="text-xs mt-1">Add a task to get started!</p>
          </div>
        ) : (
          <DragDropContext onDragEnd={handleDragEnd}>
            <Droppable droppableId="todays-tasks">
              {(provided, snapshot) => (
                <div
                  ref={provided.innerRef}
                  {...provided.droppableProps}
                  className={`space-y-1 min-h-[100px] transition-colors duration-200 ${
                    snapshot.isDraggingOver ? 'bg-orange-100/50 dark:bg-orange-900/20 rounded-lg p-2' : ''
                  }`}
                >
                  {todaysTasks.map((task, index) => (
                    <div key={task.id} className="group">
                      <TaskItem
                        task={task}
                        index={index}
                        onComplete={handleTaskComplete}
                        onEdit={handleEditTask}
                        onDelete={handleDeleteTask}
                      />
                    </div>
                  ))}
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          </DragDropContext>
        )}
      </CardContent>

      {/* Quick Add Task Dialog */}
      <Dialog open={isQuickAddOpen} onOpenChange={setIsQuickAddOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Quick Add Task</DialogTitle>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <label htmlFor="quickTitle" className="text-sm font-medium">
                Task Title
              </label>
              <Input
                id="quickTitle"
                value={quickTaskTitle}
                onChange={(e) => setQuickTaskTitle(e.target.value)}
                placeholder="What needs to be done?"
                className="bg-muted border-border"
              />
            </div>

            <div className="grid gap-2">
              <label htmlFor="quickPriority" className="text-sm font-medium">
                Priority
              </label>
              <Select
                value={quickTaskPriority}
                onValueChange={(value) => setQuickTaskPriority(value as 'low' | 'medium' | 'high')}
              >
                <SelectTrigger className="bg-muted border-border">
                  <SelectValue placeholder="Select priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="low">Low</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid gap-2">
              <label htmlFor="quickEstimation" className="text-sm font-medium">
                Time Estimation (optional)
              </label>
              <Input
                id="quickEstimation"
                value={quickTaskEstimation}
                onChange={(e) => setQuickTaskEstimation(e.target.value)}
                placeholder="e.g., 30 minutes, 2 hours"
                className="bg-muted border-border"
              />
            </div>
          </div>

          <DialogFooter>
            <Button onClick={handleQuickAdd} className="bg-orange-600 hover:bg-orange-700">
              Add Task
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Task Dialog */}
      <Dialog open={!!editingTask} onOpenChange={() => setEditingTask(null)}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Edit Task</DialogTitle>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <label htmlFor="editTitle" className="text-sm font-medium">
                Title
              </label>
              <Input
                id="editTitle"
                value={editTitle}
                onChange={(e) => setEditTitle(e.target.value)}
                placeholder="Task title"
                className="bg-muted border-border"
              />
            </div>

            <div className="grid gap-2">
              <label htmlFor="editDescription" className="text-sm font-medium">
                Description
              </label>
              <Textarea
                id="editDescription"
                value={editDescription}
                onChange={(e) => setEditDescription(e.target.value)}
                placeholder="Task description"
                rows={3}
                className="bg-muted border-border"
              />
            </div>

            <div className="grid gap-2">
              <label htmlFor="editPriority" className="text-sm font-medium">
                Priority
              </label>
              <Select
                value={editPriority}
                onValueChange={(value) => setEditPriority(value as 'low' | 'medium' | 'high')}
              >
                <SelectTrigger className="bg-muted border-border">
                  <SelectValue placeholder="Select priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="low">Low</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <DialogFooter>
            <Button onClick={handleSaveEdit}>Save Changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
};

export default TodaysTasksWidget;